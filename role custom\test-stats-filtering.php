<?php
/**
 * Test sayfası - WordPress admin sayfalarındaki istatistik sayılarının filtrelenmesi
 * Bu dosya Role Custom eklentisinin istatistik filtreleme özelliğini test etmek için kullanılır
 */

// <PERSON><PERSON>rudan er<PERSON>şim<PERSON> engelle
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test fonksiyonları
 */
class Role_Custom_Stats_Test {
    
    /**
     * Test verilerini oluştur
     */
    public static function create_test_data() {
        // Test için tutor_instructor rolünde bir kullanıcı oluştur
        $test_user = wp_create_user('test_instructor', 'test123', '<EMAIL>');
        if (!is_wp_error($test_user)) {
            $user = new WP_User($test_user);
            $user->set_role('tutor_instructor');
            
            // Test ürünleri oluştur
            for ($i = 1; $i <= 5; $i++) {
                $product_id = wp_insert_post([
                    'post_title' => 'Test Ürün ' . $i,
                    'post_content' => 'Test ürün açıklamas<PERSON> ' . $i,
                    'post_status' => ($i <= 3) ? 'publish' : 'draft',
                    'post_type' => 'product',
                    'post_author' => $test_user
                ]);
                
                if ($product_id) {
                    // WooCommerce ürün meta verilerini ekle
                    update_post_meta($product_id, '_price', '100');
                    update_post_meta($product_id, '_regular_price', '100');
                    update_post_meta($product_id, '_manage_stock', 'no');
                    update_post_meta($product_id, '_stock_status', 'instock');
                    update_post_meta($product_id, '_visibility', 'visible');
                }
            }
            
            // Test kuponları oluştur
            for ($i = 1; $i <= 3; $i++) {
                wp_insert_post([
                    'post_title' => 'Test Kupon ' . $i,
                    'post_content' => 'Test kupon açıklaması ' . $i,
                    'post_status' => ($i <= 2) ? 'publish' : 'draft',
                    'post_type' => 'shop_coupon',
                    'post_author' => $test_user
                ]);
            }
            
            // Test yazıları oluştur
            for ($i = 1; $i <= 4; $i++) {
                wp_insert_post([
                    'post_title' => 'Test Yazı ' . $i,
                    'post_content' => 'Test yazı içeriği ' . $i,
                    'post_status' => ($i <= 2) ? 'publish' : 'draft',
                    'post_type' => 'post',
                    'post_author' => $test_user
                ]);
            }
            
            return "Test verileri başarıyla oluşturuldu. Kullanıcı ID: {$test_user}";
        }
        
        return "Test kullanıcısı oluşturulamadı: " . $test_user->get_error_message();
    }
    
    /**
     * Test verilerini temizle
     */
    public static function cleanup_test_data() {
        // Test kullanıcısını bul
        $test_user = get_user_by('login', 'test_instructor');
        if ($test_user) {
            // Kullanıcının tüm postlarını sil
            $user_posts = get_posts([
                'author' => $test_user->ID,
                'post_type' => ['product', 'shop_coupon', 'post'],
                'posts_per_page' => -1,
                'post_status' => 'any'
            ]);
            
            foreach ($user_posts as $post) {
                wp_delete_post($post->ID, true);
            }
            
            // Kullanıcıyı sil
            wp_delete_user($test_user->ID);
            
            return "Test verileri başarıyla temizlendi.";
        }
        
        return "Test kullanıcısı bulunamadı.";
    }
    
    /**
     * Mevcut kullanıcının istatistiklerini göster
     */
    public static function show_current_user_stats() {
        $current_user = wp_get_current_user();
        
        if (!$current_user->ID) {
            return "Giriş yapmış kullanıcı bulunamadı.";
        }
        
        $stats = [];
        
        // Ürün sayıları
        $product_counts = [];
        $product_statuses = ['publish', 'draft', 'private', 'pending'];
        foreach ($product_statuses as $status) {
            $count = count(get_posts([
                'post_type' => 'product',
                'author' => $current_user->ID,
                'post_status' => $status,
                'posts_per_page' => -1,
                'fields' => 'ids'
            ]));
            if ($count > 0) {
                $product_counts[$status] = $count;
            }
        }
        $stats['Ürünler'] = $product_counts;
        
        // Kupon sayıları
        $coupon_counts = [];
        foreach ($product_statuses as $status) {
            $count = count(get_posts([
                'post_type' => 'shop_coupon',
                'author' => $current_user->ID,
                'post_status' => $status,
                'posts_per_page' => -1,
                'fields' => 'ids'
            ]));
            if ($count > 0) {
                $coupon_counts[$status] = $count;
            }
        }
        $stats['Kuponlar'] = $coupon_counts;
        
        // Yazı sayıları
        $post_counts = [];
        foreach ($product_statuses as $status) {
            $count = count(get_posts([
                'post_type' => 'post',
                'author' => $current_user->ID,
                'post_status' => $status,
                'posts_per_page' => -1,
                'fields' => 'ids'
            ]));
            if ($count > 0) {
                $post_counts[$status] = $count;
            }
        }
        $stats['Yazılar'] = $post_counts;
        
        $output = "Kullanıcı: {$current_user->display_name} (ID: {$current_user->ID})\n";
        $output .= "Rol: " . implode(', ', $current_user->roles) . "\n\n";
        
        foreach ($stats as $type => $counts) {
            $output .= "{$type}:\n";
            if (empty($counts)) {
                $output .= "  Hiç içerik yok\n";
            } else {
                foreach ($counts as $status => $count) {
                    $output .= "  {$status}: {$count}\n";
                }
            }
            $output .= "\n";
        }
        
        return $output;
    }
}

// Admin sayfasında test fonksiyonlarını çalıştır
if (is_admin() && current_user_can('manage_options')) {
    add_action('admin_notices', function() {
        if (isset($_GET['role_custom_test'])) {
            $action = sanitize_text_field($_GET['role_custom_test']);
            $message = '';
            
            switch ($action) {
                case 'create':
                    $message = Role_Custom_Stats_Test::create_test_data();
                    break;
                case 'cleanup':
                    $message = Role_Custom_Stats_Test::cleanup_test_data();
                    break;
                case 'stats':
                    $message = Role_Custom_Stats_Test::show_current_user_stats();
                    break;
            }
            
            if ($message) {
                echo '<div class="notice notice-info"><pre>' . esc_html($message) . '</pre></div>';
            }
        }
    });
}
